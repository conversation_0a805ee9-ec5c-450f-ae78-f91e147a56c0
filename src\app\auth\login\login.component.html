<app-two-column-auth title="LearnHub">
  <h2 class="headline">Welcome!</h2>
  <p class="sub">Sign in to your learning account</p>

  <form [formGroup]="form" (ngSubmit)="submit()" class="mt-4">
    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>User Name</mat-label>
      <input matInput formControlName="identifier" />
      @if (form.controls.identifier.hasError('required')) {
      <mat-error>Required</mat-error>
      }
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-100 mb-2">
      <mat-label>Password</mat-label>
      <input matInput [type]="hide() ? 'password' : 'text'" formControlName="password" />
      <button type="button" mat-icon-button matSuffix (click)="toggle()"
        [attr.aria-label]="'Toggle password visibility'">
        <mat-icon>{{ hide() ? 'visibility_off' : 'visibility' }}</mat-icon>
      </button>
      @if (form.controls.password.hasError('required')) {
      <mat-error>Required</mat-error>
      }
      @if (form.controls.password.hasError('minlength')) {
      <mat-error>Min 6 characters</mat-error>
      }
    </mat-form-field>

    <div class="d-flex justify-content-between align-items-center mb-3">
      <mat-checkbox formControlName="remember">Remember Me</mat-checkbox>
      <a class="link" href="javascript:void(0)">Forgot Password?</a>
    </div>

    <button mat-raised-button color="primary" class="btn-action w-100" [disabled]="loading() || form.invalid">
      @if (loading()) {
      <mat-progress-spinner diameter="18" mode="indeterminate"></mat-progress-spinner>
      }
      @if (!loading()) {
      <span>SIGN IN</span>
      }
    </button>

    <p class="mt-3 small">Don't have account yet?
      <a routerLink="/auth/register" class="link">Register now</a>
    </p>
  </form>
</app-two-column-auth>