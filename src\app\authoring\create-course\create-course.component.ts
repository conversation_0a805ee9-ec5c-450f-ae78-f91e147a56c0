import { Component, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, Validators, FormArray } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MATERIAL } from '../../shared/material.imports';
import { MatDialog } from '@angular/material/dialog';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

import { HeaderComponent } from '../../shared/ui/header/header.component';
import { AuthoringService } from '../../core/services/authoring.service';
import { AuthService } from '../../core/services/auth.service';
import { LectureModalComponent } from '../lecture-modal/lecture-modal.component';

@Component({
    selector: 'app-create-course',
    standalone: true,
    imports: [
        CommonModule, RouterLink, ReactiveFormsModule, DragDropModule,
        HeaderComponent, ...MATERIAL
    ],
    templateUrl: './create-course.component.html',
    styleUrls: ['./create-course.component.scss']
})
export class CreateCourseComponent {
    private fb = inject(FormBuilder);
    private dialog = inject(MatDialog);
    private authoring = inject(AuthoringService);
    private auth = inject(AuthService);
    private router = inject(Router);

    step = signal<1 | 2 | 3>(1);
    publishing = signal(false);

    // Step 1: Basic details
    basicForm = this.fb.nonNullable.group({
        title: ['', [Validators.required, Validators.minLength(3)]],
        subtitle: ['', [Validators.required, Validators.minLength(10)]],
        thumbnailUrl: [''],
        providerName: ['Self-Published', Validators.required],
        providerLogo: ['https://i.imgur.com/coursera.png', Validators.required],
        difficulty: ['Beginner', Validators.required]
    });

    // Step 2: Curriculum builder (FormArray of sections)
    sections = this.fb.array<FormArray>([]);
    get sectionsControls() { return this.sections.controls as any[]; }

    // Step 3: Overview metadata
    overviewForm = this.fb.nonNullable.group({
        durationText: ['05 Weeks', Validators.required],
        whatYoullLearn: ['Gain skills; Work with data; Create dashboards'],
        skills: ['Data Analysis, SQL, Visualization'],
        requirements: ['No experience required.']
    });

    // Derived completeness flags
    step1Valid = computed(() => this.basicForm.valid);
    step2Valid = computed(() => this.sections.length > 0 && this.sections.controls.every(sec => (sec as any).value.title?.trim()?.length));
    step3Valid = computed(() => this.overviewForm.valid);

    addSection(title = '') {
        const section = this.fb.nonNullable.group({
            title: [title, Validators.required],
            lectures: this.fb.array<any>([])
        });
        this.sections.push(section as any);
    }

    removeSection(i: number) { this.sections.removeAt(i); }
    moveSection(evt: CdkDragDrop<any[]>) { moveItemInArray(this.sections.controls, evt.previousIndex, evt.currentIndex); }

    // lectures
    lecturesArray(i: number) { return (this.sections.at(i).get('lectures') as FormArray); }
    moveLecture(i: number, evt: CdkDragDrop<any[]>) { moveItemInArray(this.lecturesArray(i).controls, evt.previousIndex, evt.currentIndex); }

    openAddLecture(i: number) {
        this.dialog.open(LectureModalComponent, { width: '560px' }).afterClosed().subscribe(res => {
            if (!res) return;
            const lect = this.fb.nonNullable.group({
                title: [res.title, Validators.required],
                type: [res.type as 'Video' | 'Text' | 'PDF', Validators.required],
                durationMinutes: [res.durationMinutes, Validators.required],
                content: [res.content]
            });
            this.lecturesArray(i).push(lect);
        });
    }

    // navigation
    next() { if (this.step() === 1 && !this.step1Valid()) return; if (this.step() === 2 && !this.step2Valid()) return; this.step.set((this.step() + 1) as any); }
    prev() { if (this.step() === 1) return; this.step.set((this.step() - 1) as any); }

    // Publish flow
    publish() {
        if (!(this.step1Valid() && this.step2Valid() && this.step3Valid())) return;
        this.publishing.set(true);

        const userId = this.auth.currentUser!.id;
        const basic = this.basicForm.getRawValue();
        const meta = this.overviewForm.getRawValue();

        const payload = {
            title: basic.title,
            subtitle: basic.subtitle,
            thumbnailUrl: basic.thumbnailUrl || 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
            authorId: userId,
            provider: { name: basic.providerName, logoUrl: basic.providerLogo },
            difficulty: basic.difficulty as any,
            durationText: meta.durationText,
            skills: meta.skills.split(',').map(s => s.trim()).filter(Boolean),
            whatYoullLearn: meta.whatYoullLearn.split(';').map(s => s.trim()).filter(Boolean),
            requirements: meta.requirements.split(';').map(s => s.trim()).filter(Boolean)
        };

        this.authoring.createDraftCourse(payload as any).pipe(
            // curriculum
            switchMap((courseId: number) => {
                const sections = this.sections.getRawValue().map((s, idx) => ({
                    id: Date.now() + idx,
                    title: s.title,
                    lectures: s.lectures.map((l: any, j: number) => ({
                        id: Date.now() + idx * 100 + j,
                        title: l.title,
                        type: l.type,
                        durationMinutes: +l.durationMinutes,
                        content: l.content
                    }))
                }));
                return this.authoring.upsertCurriculum(courseId, sections).pipe(map(() => courseId));
            }),
            // publish
            switchMap((courseId: number) => this.authoring.publishCourse(courseId).pipe(map(() => courseId)))
        ).subscribe({
            next: (courseId) => {
                this.publishing.set(false);
                alert('Your course published successfully!');
                this.router.navigate(['/courses', courseId]);
            },
            error: () => { this.publishing.set(false); }
        });
    }
}
