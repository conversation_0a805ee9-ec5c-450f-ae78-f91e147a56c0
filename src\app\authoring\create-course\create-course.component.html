<!-- src/app/authoring/create-course/create-course.component.html -->
<app-header></app-header>

<div class="create-course container-xxl py-4">
    <!-- breadcrumb -->
    <nav class="breadcrumb-wrap">
        <a routerLink="/dashboard" class="crumb">Home</a>
        <span class="sep">›</span>
        <a routerLink="/author/courses" class="crumb">My Courses</a>
        <span class="sep">›</span>
        <span class="crumb current">Create New Course</span>
    </nav>

    <div class="title-row d-flex align-items-center justify-content-between mb-3">
        <h2 class="m-0">Create New Course</h2>
        <button mat-raised-button color="primary" class="publish-btn" (click)="publish()"
            [disabled]="!(step1Valid() && step2Valid() && step3Valid()) || publishing()">
            Publish
        </button>
    </div>

    <!-- Stepper -->
    <div class="stepper card mb-3">
        <div class="step" [class.active]="step()===1"><span>01</span> Basic Details</div>
        <div class="sep-dot"></div>
        <div class="step" [class.active]="step()===2"><span>02</span> Course Content</div>
        <div class="sep-dot"></div>
        <div class="step" [class.active]="step()===3"><span>03</span> Overview</div>
    </div>

    <!-- STEP 1 -->
    @if (step()===1) {
    <div class="card p-4">
        <h6 class="mb-3">Details</h6>

        <form [formGroup]="basicForm" class="grid">
            <mat-form-field appearance="fill">
                <mat-label>Course Title</mat-label>
                <input matInput formControlName="title" placeholder="Enter Course title here" />
            </mat-form-field>

            <mat-form-field appearance="fill" class="full">
                <mat-label>Description</mat-label>
                <textarea matInput rows="4" formControlName="subtitle" placeholder="Enter Description here"></textarea>
            </mat-form-field>

            <div class="full">
                <label class="label">Upload Course thumbnail</label>
                <div class="drop-zone">
                    <input type="file" accept="image/*" (change)="onFileSelected($event)">
                    <div class="hint">Drag and drop file to upload or, <span class="link">Choose file</span></div>
                </div>
                @if (basicForm.value.thumbnailUrl) {
                <img class="thumb-preview" [src]="basicForm.value.thumbnailUrl" alt="thumb" />
                }
            </div>

            <mat-form-field appearance="fill">
                <mat-label>Provider name</mat-label>
                <input matInput formControlName="providerName" />
            </mat-form-field>

            <mat-form-field appearance="fill">
                <mat-label>Provider logo URL</mat-label>
                <input matInput formControlName="providerLogo" />
            </mat-form-field>

            <mat-form-field appearance="fill">
                <mat-label>Level</mat-label>
                <mat-select formControlName="difficulty">
                    <mat-option value="Beginner">Beginner</mat-option>
                    <mat-option value="Intermediate">Intermediate</mat-option>
                    <mat-option value="Advanced">Advanced</mat-option>
                </mat-select>
            </mat-form-field>
        </form>
    </div>
    }

    <!-- STEP 2 -->
    @if (step()===2) {
    <div class="card p-4">
        <div class="d-flex align-items-center justify-content-between mb-2">
            <h6 class="m-0">Modules</h6>
            <button mat-stroked-button (click)="addSection('New Section')">Add New Section</button>
        </div>

        <div cdkDropList (cdkDropListDropped)="moveSection($event)">
            @for (sec of sectionsControls; track $index) {
            <div class="section card inner mb-2" cdkDrag>
                <div class="section-head d-flex align-items-center gap-2">
                    <span class="drag">⋮⋮</span>
                    <mat-form-field appearance="fill" class="flex-1">
                        <mat-label>Enter module title here</mat-label>
                        <input matInput [formControl]="sec.controls.title" />
                    </mat-form-field>
                    <button mat-icon-button color="warn"
                        (click)="removeSection($index)"><mat-icon>delete</mat-icon></button>
                </div>

                <div class="lectures">
                    <div cdkDropList (cdkDropListDropped)="moveLecture($index,$event)">
                        @for (lec of sec.controls.lectures.controls; track $index) {
                        <div class="lecture" cdkDrag>
                            <span class="drag">⋮⋮</span>
                            <span class="type">{{ lec.value.type }}</span>
                            <span class="t">{{ lec.value.title }}</span>
                            <span class="time">{{ lec.value.durationMinutes }} mins</span>
                        </div>
                        }
                    </div>

                    <button mat-raised-button color="primary" (click)="openAddLecture($index)">Add Lecture</button>
                </div>
            </div>
            }
        </div>
    </div>
    }

    <!-- STEP 3 -->
    @if (step()===3) {
    <div class="card p-4">
        <h6 class="mb-3">Overview</h6>
        <form [formGroup]="overviewForm" class="grid">
            <mat-form-field appearance="fill">
                <mat-label>Duration</mat-label>
                <input matInput formControlName="durationText" placeholder="e.g. 05 Weeks" />
            </mat-form-field>

            <mat-form-field appearance="fill">
                <mat-label>Level</mat-label>
                <input matInput [value]="basicForm.value.difficulty" disabled />
            </mat-form-field>

            <mat-form-field appearance="fill" class="full">
                <mat-label>What you will learn (use ; to separate)</mat-label>
                <textarea matInput rows="2" formControlName="whatYoullLearn"></textarea>
            </mat-form-field>

            <mat-form-field appearance="fill" class="full">
                <mat-label>What skills you will gain (comma separated)</mat-label>
                <textarea matInput rows="2" formControlName="skills"></textarea>
            </mat-form-field>

            <mat-form-field appearance="fill" class="full">
                <mat-label>Prerequisites / Software Requirements (use ; to separate)</mat-label>
                <textarea matInput rows="2" formControlName="requirements"></textarea>
            </mat-form-field>
        </form>
    </div>
    }

    <!-- Wizard nav -->
    <div class="wizard-nav d-flex align-items-center justify-content-between mt-3">
        <button mat-stroked-button (click)="prev()" [disabled]="step()===1">Prev</button>
        <button mat-raised-button color="primary" (click)="next()"
            [disabled]="(step()===1 && !step1Valid()) || (step()===2 && !step2Valid()) || step()===3">
            Next
        </button>
    </div>
</div>