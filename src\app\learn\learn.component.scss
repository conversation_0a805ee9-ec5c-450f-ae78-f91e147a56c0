:host {
    display: block;
    background: var(--bg);
    color: var(--text);
}

.card {
    background: var(--surface);
    border: 1px solid #1b2a38;
    border-radius: 16px;
}

.breadcrumb-wrap {
    color: var(--muted);
}

.crumb {
    color: #9ad4ff;
    text-decoration: none;
}

.crumb.current {
    color: var(--text);
}

.sep {
    margin: 0 6px;
    color: #6a7e95;
}

.below-tabs ::ng-deep .mat-mdc-tab-header {
    background: transparent;
    border-bottom: 0;
}