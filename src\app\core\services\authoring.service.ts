// src/app/core/services/authoring.service.ts
import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, switchMap } from 'rxjs';

export interface NewCoursePayload {
    title: string;
    subtitle: string;
    thumbnailUrl: string;
    authorId: number;
    provider: { name: string; logoUrl: string };
    difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
    durationText: string;
    skills: string[];
    whatYoullLearn: string[];
    requirements: string[];
    status: 'Draft' | 'Published' | 'Archived';
    publishedDate: string;
    rating: number;
    reviewCount: number;
    enrollmentCount: number;
}

export interface CurriculumPayload {
    courseId: number;
    sections: {
        id: number;
        title: string;
        lectures: { id: number; title: string; type: 'Video' | 'Text' | 'PDF'; durationMinutes: number; content: any }[];
    }[];
}

@Injectable({ providedIn: 'root' })
export class AuthoringService {
    private http = inject(HttpClient);
    private API = 'http://localhost:3000';

    private nextId(coll: string) {
        return this.http.get<any[]>(`${this.API}/${coll}`).pipe(
            map(list => (Math.max(0, ...list.map(i => +i.id || +i.courseId || 0)) + 1))
        );
    }

    createDraftCourse(body: Omit<NewCoursePayload, 'status' | 'publishedDate' | 'rating' | 'reviewCount' | 'enrollmentCount'>) {
        const payload: NewCoursePayload = {
            ...body,
            status: 'Draft',
            publishedDate: new Date().toISOString(),
            rating: 0, reviewCount: 0, enrollmentCount: 0
        };
        return this.nextId('courses').pipe(
            switchMap(id => this.http.post(`${this.API}/courses`, { id, ...payload }).pipe(map(() => id)))
        );
    }

    upsertCurriculum(courseId: number, sections: CurriculumPayload['sections']) {
        // if curriculum for this course exists -> PATCH; else POST
        return this.http.get<any[]>(`${this.API}/curriculum?courseId=${courseId}`).pipe(
            switchMap(rows => rows.length
                ? this.http.patch(`${this.API}/curriculum/${rows[0].id}`, { courseId, sections })
                : this.http.post(`${this.API}/curriculum`, { id: Date.now(), courseId, sections }))
        );
    }

    publishCourse(courseId: number) {
        return this.http.patch(`${this.API}/courses/${courseId}`, {
            status: 'Published', publishedDate: new Date().toISOString()
        });
    }
}
