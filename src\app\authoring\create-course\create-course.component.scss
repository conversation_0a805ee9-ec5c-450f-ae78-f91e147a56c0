/* src/app/authoring/create-course/create-course.component.scss */
:host {
    display: block;
    background: var(--bg);
    color: var(--text);
}

.card {
    background: var(--surface);
    border: 1px solid #1b2a38;
    border-radius: 16px;
}

.inner {
    background: var(--surface-2);
}

.publish-btn {
    background: var(--brand);
    color: #052214;
    font-weight: 700;
}

.breadcrumb-wrap {
    color: var(--muted);
    margin-bottom: 6px;

    .crumb {
        color: #9ad4ff;
        text-decoration: none;
    }

    .crumb.current {
        color: var(--text);
    }

    .sep {
        margin: 0 6px;
        color: #6a7e95;
    }
}

.stepper {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 14px;
}

.step {
    padding: 8px 10px;
    border-radius: 999px;
    background: #17212b;
    color: #9fb2c7;
    font-weight: 700;
}

.step.active {
    background: #263546;
    color: #e6eef7;
}

.step span {
    opacity: .85;
    margin-right: 6px;
}

.sep-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2b3a4a;
}

.grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.grid .full {
    grid-column: 1/-1;
}

.label {
    display: block;
    margin-bottom: 6px;
    color: var(--muted);
}

.drop-zone {
    border: 1px dashed #2a3b4e;
    border-radius: 12px;
    padding: 18px;
    text-align: center;
    background: #16202b;
}

.drop-zone input {
    display: none;
}

.drop-zone .link {
    text-decoration: underline;
    cursor: pointer;
}

.thumb-preview {
    margin-top: 8px;
    width: 240px;
    height: 140px;
    object-fit: cover;
    border-radius: 12px;
    border: 1px solid #2a3b4e;
}

.section {
    padding: 10px;
}

.section-head .drag {
    cursor: grab;
    user-select: none;
}

.section-head .flex-1 {
    flex: 1;
}

.lectures {
    padding: 8px 8px 12px;
}

.lecture {
    display: grid;
    grid-template-columns: 24px 80px 1fr auto;
    align-items: center;
    gap: 8px;
    padding: 8px;
    margin: 6px 0;
    border-radius: 8px;
    background: #1a2330;
    border: 1px solid #263546;
}

.lecture .drag {
    cursor: grab;
}

.lecture .type {
    color: #8cc0ff;
    font-weight: 700;
}

.lecture .time {
    color: var(--muted);
}

.wizard-nav {
    position: sticky;
    bottom: 12px;
    padding-bottom: 12px;
    background: transparent;
}

@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
}