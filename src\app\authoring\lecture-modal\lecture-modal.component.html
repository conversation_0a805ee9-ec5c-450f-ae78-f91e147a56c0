<h3 mat-dialog-title>Add New Lecture</h3>

<div mat-dialog-content class="dialog-body">
    <form [formGroup]="form" class="form">
        <mat-form-field appearance="fill">
            <mat-label>Type</mat-label>
            <mat-select formControlName="type">
                <mat-option value="Video">Video</mat-option>
                <mat-option value="Text">Topic Content editor</mat-option>
                <mat-option value="PDF">PDF</mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field appearance="fill">
            <mat-label>Topic Title</mat-label>
            <input matInput formControlName="title" />
        </mat-form-field>

        <mat-form-field appearance="fill">
            <mat-label>Topic Description</mat-label>
            <textarea matInput rows="3" formControlName="description"></textarea>
        </mat-form-field>

        @if (form.value.type === 'Video') {
        <mat-form-field appearance="fill">
            <mat-label>Video link (YouTube or mp4)</mat-label>
            <input matInput formControlName="videoLink" placeholder="https://youtu.be/..." />
        </mat-form-field>
        }

        @if (form.value.type === 'Text') {
        <mat-form-field appearance="fill">
            <mat-label>Topic HTML</mat-label>
            <textarea matInput rows="6" formControlName="htmlContent" placeholder="<h2>Intro</h2>..."></textarea>
        </mat-form-field>
        }

        @if (form.value.type === 'PDF') {
        <mat-form-field appearance="fill">
            <mat-label>PDF file name or URL</mat-label>
            <input matInput formControlName="fileName" placeholder="path/to/file.pdf" />
        </mat-form-field>
        }

        <mat-form-field appearance="fill">
            <mat-label>Duration (minutes)</mat-label>
            <input matInput type="number" min="1" formControlName="durationMinutes" />
        </mat-form-field>
    </form>
</div>

<div mat-dialog-actions [align]="'end'">
    <button mat-stroked-button (click)="cancel()">Cancel</button>
    <button mat-raised-button color="primary" (click)="submit()" [disabled]="form.invalid">Submit</button>
</div>