{"name": "huangular2022", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "api": "json-server api/db.json", "lint": "ng lint", "format": "npx prettier 'src/**/*.{js,jsx,ts,tsx,html,css,scss}' --write"}, "private": true, "dependencies": {"@angular/animations": "^20.2.1", "@angular/cdk": "^20.2.0", "@angular/common": "^20.2.1", "@angular/compiler": "^20.2.1", "@angular/core": "^20.2.1", "@angular/forms": "^20.2.1", "@angular/material": "^20.2.0", "@angular/platform-browser": "^20.2.1", "@angular/platform-browser-dynamic": "^20.2.1", "@angular/router": "^20.2.1", "bootstrap": "^5.3.8", "rxjs": "^7.8.2", "tslib": "^2.3.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.2.0", "@angular-eslint/builder": "^20.2.0", "@angular-eslint/eslint-plugin": "^20.2.0", "@angular-eslint/eslint-plugin-template": "^20.2.0", "@angular-eslint/schematics": "^20.2.0", "@angular-eslint/template-parser": "^20.2.0", "@angular/cli": "^20.2.0", "@angular/compiler-cli": "^20.2.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.11.0", "@typescript-eslint/parser": "5.11.0", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jasmine-core": "~3.10.0", "json-server": "^1.0.0-beta.3", "karma": "^6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "prettier": "^2.5.1", "typescript": "~5.8.3"}}