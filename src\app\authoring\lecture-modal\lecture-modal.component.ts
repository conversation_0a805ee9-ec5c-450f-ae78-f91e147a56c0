// src/app/authoring/lecture-modal/lecture-modal.component.ts
import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { MATERIAL } from '../../shared/material.imports';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'app-lecture-modal',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, ...MATERIAL],
    templateUrl: './lecture-modal.component.html',
    styleUrls: ['./lecture-modal.component.scss']
})
export class LectureModalComponent {
    private fb = inject(FormBuilder);
    private ref = inject(MatDialogRef<LectureModalComponent>);

    form = this.fb.nonNullable.group({
        type: ['Video' as 'Video' | 'Text' | 'PDF', Validators.required],
        title: ['', [Validators.required, Validators.minLength(3)]],
        description: [''],
        durationMinutes: [5, [Validators.required, Validators.min(1)]],
        // conditional
        videoLink: [''],
        htmlContent: [''],
        fileName: ['']
    });

    submit() {
        if (this.form.invalid) return;
        const v = this.form.getRawValue();
        let content: any = {};
        if (v.type === 'Video') content = { videoUrl: v.videoLink || 'https://youtu.be/M7lc1UVf-VE' };
        if (v.type === 'Text') content = { htmlContent: v.htmlContent || '<p>No content</p>' };
        if (v.type === 'PDF') content = { fileUrl: v.fileName || 'path/to/sample.pdf' };

        this.ref.close({
            title: v.title,
            type: v.type,
            durationMinutes: v.durationMinutes,
            content
        });
    }
    cancel() { this.ref.close(null); }
}
